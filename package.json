{"name": "tr-restoran-pos", "version": "1.0.0", "main": "dist/index.js", "scripts": {"dev": "concurrently \"npm:dev:*\"", "dev:next": "next dev -p 3000", "dev:electron": "cross-env NODE_ENV=development wait-on http://localhost:3000 && npm run build:main && cross-env NODE_ENV=development electron .", "build": "next build && npm run build:main && electron-builder", "build:main": "npx tsc src/main/index.ts src/main/preload.ts --outDir dist --target ES2020 --module commonjs --esModuleInterop", "lint": "next lint", "lint:fix": "next lint --fix", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["pos", "restaurant", "electron", "nextjs"], "author": "", "license": "ISC", "description": "Turkish Restaurant POS MVP with PostgreSQL + Prisma + Electron + shadcn/ui", "dependencies": {"@prisma/client": "^6.11.1", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "concurrently": "^9.2.0", "dayjs": "^1.11.13", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "^15.3.5", "pg": "^8.16.3", "postcss": "^8.5.6", "prisma": "^6.11.1", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.13", "@types/pg": "^8.15.4", "@types/react": "^19.1.8", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "cross-env": "^7.0.3", "electron": "^37.2.1", "electron-builder": "^26.0.12", "eslint": "^9.31.0", "eslint-config-next": "^15.3.5", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "wait-on": "^8.0.3"}, "build": {"appId": "com.turkiye.restoran.pos", "productName": "Türkiye Restoran POS", "directories": {"output": "release"}, "files": ["dist/**/*", "out/**/*", "node_modules/**/*", "package.json"], "extraMetadata": {"main": "dist/index.js"}, "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}