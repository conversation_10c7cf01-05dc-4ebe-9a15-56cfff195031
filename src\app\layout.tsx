import type { Metadata } from 'next';
import './globals.css';

export const metadata: Metadata = {
  title: '🇹🇷 Türkiye Restoran POS',
  description: 'Turkish Restaurant POS MVP with PostgreSQL + Prisma + Electron + shadcn/ui',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="tr">
      <body className="antialiased">
        {children}
      </body>
    </html>
  );
}
