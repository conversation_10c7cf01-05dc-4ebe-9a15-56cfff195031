'use client';

import { Button } from '@/components/ui/button';
import { useState, useEffect } from 'react';

export default function HealthPage() {
  const [dbStatus, setDbStatus] = useState<string>('Checking...');
  const [theme, setTheme] = useState<'light' | 'dark'>('light');

  useEffect(() => {
    // Simulate database check
    // In a real app, this would be an API call to check PostgreSQL connection
    setTimeout(() => {
      setDbStatus('PostgreSQL: Connected (simulated)');
    }, 1000);
  }, []);

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    document.documentElement.classList.toggle('dark', newTheme === 'dark');
  };

  return (
    <main className="p-8 min-h-screen bg-background text-foreground">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-6">🇹🇷 Restoran POS – Health Check</h1>
        
        <div className="grid gap-6">
          <div className="p-6 border rounded-lg bg-card">
            <h2 className="text-2xl font-semibold mb-4">System Status</h2>
            <div className="space-y-2">
              <p className="text-lg">
                <span className="font-medium">Database:</span> {dbStatus}
              </p>
              <p className="text-lg">
                <span className="font-medium">Electron:</span> ✅ Running
              </p>
              <p className="text-lg">
                <span className="font-medium">Next.js:</span> ✅ Active
              </p>
              <p className="text-lg">
                <span className="font-medium">Tailwind CSS:</span> ✅ Loaded
              </p>
            </div>
          </div>

          <div className="p-6 border rounded-lg bg-card">
            <h2 className="text-2xl font-semibold mb-4">Quick Actions</h2>
            <div className="flex gap-4">
              <Button onClick={toggleTheme}>
                {theme === 'light' ? '🌙' : '☀️'} Toggle Theme
              </Button>
              <Button variant="outline">
                📊 View Dashboard (Coming Soon)
              </Button>
              <Button variant="secondary">
                ⚙️ Settings (Coming Soon)
              </Button>
            </div>
          </div>

          <div className="p-6 border rounded-lg bg-card">
            <h2 className="text-2xl font-semibold mb-4">Tech Stack</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div className="p-4 bg-secondary rounded-lg">
                <div className="text-2xl mb-2">⚡</div>
                <div className="font-medium">Electron</div>
              </div>
              <div className="p-4 bg-secondary rounded-lg">
                <div className="text-2xl mb-2">⚛️</div>
                <div className="font-medium">Next.js</div>
              </div>
              <div className="p-4 bg-secondary rounded-lg">
                <div className="text-2xl mb-2">🗄️</div>
                <div className="font-medium">PostgreSQL</div>
              </div>
              <div className="p-4 bg-secondary rounded-lg">
                <div className="text-2xl mb-2">🎨</div>
                <div className="font-medium">Tailwind</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
